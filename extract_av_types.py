#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从HTML文件中提取日本AV类型种类的脚本
"""

import re
import os
from bs4 import BeautifulSoup

def extract_av_types_from_html(html_file_path):
    """从HTML文件中提取AV类型"""
    
    # 读取HTML文件
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 常见的AV类型关键词列表
    av_types = set()
    
    # 定义常见的AV类型关键词
    common_av_types = [
        # 人物类型
        '熟女', '人妻', '美少女', 'ロリ', 'ギャル', 'お姉さん', '若妻', '未亡人',
        '女教師', '女医', 'CA', '秘書', '女子校生', 'JK', 'JD', 'OL', 'ナース',
        
        # 服装类型
        '制服', '学生', 'メイド', 'コスプレ', 'ランジェリー', '下着', 'パンスト',
        'ストッキング', 'ハイソックス', 'ニーハイ', '競泳水着', 'スクール水着',
        'ビキニ', '浴衣', '着物', 'チャイナドレス', 'バニーガール', 'レースクイーン',
        'キャンギャル', 'チアガール', '婦警', '看護師', '巫女', '尼僧', '修道女',
        
        # 身体特征
        '巨乳', '美乳', '貧乳', '巨尻', '美脚', '美尻', 'スレンダー', 'ぽっちゃり',
        
        # 内容类型
        '素人', '企画', 'ドキュメンタリー', 'ハメ撮り', 'ナンパ', '逆ナン', '痴漢',
        'レイプ', '輪姦', '乱交', '3P', '4P', 'アナル', 'フェラ', '手コキ', 'パイズリ',
        'クンニ', '潮吹き', '中出し', '顔射', '口内射精', 'ごっくん', 'ぶっかけ',
        
        # SM・特殊プレイ
        'SM', '拘束', '調教', '羞恥', '露出', '野外', '温泉', '風呂', 'マッサージ',
        'エステ', '盗撮', 'のぞき', '着替え', 'パンチラ', '胸チラ',
        
        # その他
        'VR', 'AI', 'アニメ', 'CG', '3D', 'ゲーム', 'ライブチャット', 'オナニー',
        'バイブ', 'ローター', 'ディルド', 'オナホール', 'コスメ', 'ソープ',
        'ヘルス', 'デリヘル', 'ピンサロ', 'イメクラ', 'M性感', 'SM倶楽部'
    ]
    
    # 在HTML内容中搜索这些关键词
    for av_type in common_av_types:
        # 使用正则表达式搜索，确保是完整的词
        pattern = r'\b' + re.escape(av_type) + r'\b'
        if re.search(pattern, html_content):
            av_types.add(av_type)
    
    # 也尝试从链接和标签中提取
    links = soup.find_all('a')
    for link in links:
        if link.get('href') and 'genre' in link.get('href', ''):
            text = link.get_text(strip=True)
            if text and len(text) < 20:  # 避免太长的文本
                for av_type in common_av_types:
                    if av_type in text:
                        av_types.add(av_type)
    
    # 查找包含类型信息的div或span标签
    for tag in soup.find_all(['div', 'span', 'li', 'td']):
        text = tag.get_text(strip=True)
        if text and len(text) < 50:  # 避免太长的文本
            for av_type in common_av_types:
                if av_type in text:
                    av_types.add(av_type)
    
    return sorted(list(av_types))

def save_to_txt(av_types, output_file):
    """将AV类型保存到txt文件"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("日本AV类型种类列表\n")
            f.write("=" * 30 + "\n\n")
            
            # 按类别分组
            categories = {
                '人物类型': ['熟女', '人妻', '美少女', 'ロリ', 'ギャル', 'お姉さん', '若妻', '未亡人',
                           '女教師', '女医', 'CA', '秘書', '女子校生', 'JK', 'JD', 'OL', 'ナース'],
                '服装类型': ['制服', '学生', 'メイド', 'コスプレ', 'ランジェリー', '下着', 'パンスト',
                           'ストッキング', 'ハイソックス', 'ニーハイ', '競泳水着', 'スクール水着',
                           'ビキニ', '浴衣', '着物', 'チャイナドレス', 'バニーガール', 'レースクイーン',
                           'キャンギャル', 'チアガール', '婦警', '看護師', '巫女', '尼僧', '修道女'],
                '身体特征': ['巨乳', '美乳', '貧乳', '巨尻', '美脚', '美尻', 'スレンダー', 'ぽっちゃり'],
                '内容类型': ['素人', '企画', 'ドキュメンタリー', 'ハメ撮り', 'ナンパ', '逆ナン', '痴漢',
                           'レイプ', '輪姦', '乱交', '3P', '4P', 'アナル', 'フェラ', '手コキ', 'パイズリ',
                           'クンニ', '潮吹き', '中出し', '顔射', '口内射精', 'ごっくん', 'ぶっかけ'],
                'SM・特殊プレイ': ['SM', '拘束', '調教', '羞恥', '露出', '野外', '温泉', '風呂', 'マッサージ',
                                'エステ', '盗撮', 'のぞき', '着替え', 'パンチラ', '胸チラ'],
                'その他': ['VR', 'AI', 'アニメ', 'CG', '3D', 'ゲーム', 'ライブチャット', 'オナニー',
                          'バイブ', 'ローター', 'ディルド', 'オナホール', 'コスメ', 'ソープ',
                          'ヘルス', 'デリヘル', 'ピンサロ', 'イメクラ', 'M性感', 'SM倶楽部']
            }
            
            for category, types in categories.items():
                found_types = [t for t in types if t in av_types]
                if found_types:
                    f.write(f"{category}:\n")
                    for av_type in found_types:
                        f.write(f"  - {av_type}\n")
                    f.write("\n")
            
            # 写入所有找到的类型
            f.write("所有找到的类型 (按字母顺序):\n")
            f.write("-" * 30 + "\n")
            for i, av_type in enumerate(av_types, 1):
                f.write(f"{i:3d}. {av_type}\n")
            
            f.write(f"\n总计找到 {len(av_types)} 种类型\n")
        
        print(f"成功保存到 {output_file}")
        print(f"总计找到 {len(av_types)} 种AV类型")
        
    except Exception as e:
        print(f"保存文件失败: {e}")

def main():
    """主函数"""
    html_file = "1.html"
    output_file = "av_types.txt"
    
    if not os.path.exists(html_file):
        print(f"HTML文件 {html_file} 不存在")
        return
    
    print("开始提取AV类型...")
    av_types = extract_av_types_from_html(html_file)
    
    if av_types:
        save_to_txt(av_types, output_file)
        
        # 显示前20个类型作为预览
        print("\n找到的类型预览 (前20个):")
        for i, av_type in enumerate(av_types[:20], 1):
            print(f"{i:2d}. {av_type}")
        
        if len(av_types) > 20:
            print(f"... 还有 {len(av_types) - 20} 个类型")
    else:
        print("未找到任何AV类型")

if __name__ == "__main__":
    main()
